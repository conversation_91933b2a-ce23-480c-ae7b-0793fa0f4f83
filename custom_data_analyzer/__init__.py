# Custom Data Analyzer Plugin for VeighNa
from pathlib import Path
from vnpy.trader.app import BaseApp
from .engine import DataAnalyzerEngine

APP_NAME = "DataAnalyzer"

class DataAnalyzerApp(BaseApp):
    """
    Custom data analyzer application for VeighNa platform.
    """
    
    app_name: str = APP_NAME
    app_module: str = __module__
    app_path: Path = Path(__file__).parent
    display_name: str = "数据分析"
    engine_class: type[DataAnalyzerEngine] = DataAnalyzerEngine
    widget_name: str = "DataAnalyzerWidget"
    icon_name: str = str(app_path.joinpath("ui", "analyzer.ico"))

__all__ = ["DataAnalyzerApp", "APP_NAME"]
