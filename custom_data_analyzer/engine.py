"""
Data Analyzer Engine for custom analysis functionality.
"""

from typing import Dict, List
from datetime import datetime
import pandas as pd

from vnpy.event import Event, EventEngine
from vnpy.trader.engine import BaseEngine, MainEngine
from vnpy.trader.object import BarData, TickData
from vnpy.trader.database import get_database

# Custom events
EVENT_ANALYZER_LOG = "eAnalyzerLog"
EVENT_ANALYZER_RESULT = "eAnalyzerResult"

APP_NAME = "DataAnalyzer"


class DataAnalyzerEngine(BaseEngine):
    """
    Engine for data analysis functionality.
    """

    def __init__(self, main_engine: MainEngine, event_engine: EventEngine) -> None:
        """Initialize the engine."""
        super().__init__(main_engine, event_engine, APP_NAME)
        
        self.database = get_database()
        self.analysis_results: Dict = {}
        
    def init_engine(self) -> None:
        """Initialize the engine."""
        self.write_log("数据分析引擎初始化完成")
        
    def write_log(self, msg: str) -> None:
        """Write log message."""
        event = Event(EVENT_ANALYZER_LOG)
        event.data = msg
        self.event_engine.put(event)
        
    def analyze_symbol_data(self, vt_symbol: str, start_date: datetime, end_date: datetime) -> Dict:
        """
        Analyze historical data for a specific symbol.
        
        Args:
            vt_symbol: Symbol to analyze
            start_date: Start date for analysis
            end_date: End date for analysis
            
        Returns:
            Dictionary containing analysis results
        """
        try:
            self.write_log(f"开始分析 {vt_symbol} 的历史数据...")
            
            # Load historical data from database
            bars = self.database.load_bar_data(
                symbol=vt_symbol.split('.')[0],
                exchange=vt_symbol.split('.')[1],
                interval="1m",
                start=start_date,
                end=end_date
            )
            
            if not bars:
                self.write_log(f"未找到 {vt_symbol} 的历史数据")
                return {}
                
            # Convert to DataFrame for analysis
            data = []
            for bar in bars:
                data.append({
                    'datetime': bar.datetime,
                    'open': bar.open_price,
                    'high': bar.high_price,
                    'low': bar.low_price,
                    'close': bar.close_price,
                    'volume': bar.volume
                })
                
            df = pd.DataFrame(data)
            
            # Perform basic statistical analysis
            results = {
                'symbol': vt_symbol,
                'total_bars': len(df),
                'date_range': f"{start_date.date()} to {end_date.date()}",
                'price_stats': {
                    'max_price': df['high'].max(),
                    'min_price': df['low'].min(),
                    'avg_price': df['close'].mean(),
                    'price_volatility': df['close'].std()
                },
                'volume_stats': {
                    'total_volume': df['volume'].sum(),
                    'avg_volume': df['volume'].mean(),
                    'max_volume': df['volume'].max()
                },
                'returns': {
                    'total_return': (df['close'].iloc[-1] / df['close'].iloc[0] - 1) * 100,
                    'daily_returns': df['close'].pct_change().dropna().tolist()
                }
            }
            
            self.analysis_results[vt_symbol] = results
            
            # Send result event
            event = Event(EVENT_ANALYZER_RESULT)
            event.data = results
            self.event_engine.put(event)
            
            self.write_log(f"{vt_symbol} 数据分析完成")
            return results
            
        except Exception as e:
            error_msg = f"分析 {vt_symbol} 数据时发生错误: {str(e)}"
            self.write_log(error_msg)
            return {}
            
    def get_analysis_results(self) -> Dict:
        """Get all analysis results."""
        return self.analysis_results
        
    def clear_results(self) -> None:
        """Clear all analysis results."""
        self.analysis_results.clear()
        self.write_log("分析结果已清空")
