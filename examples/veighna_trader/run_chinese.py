#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
VeighNa交易系统中文启动脚本
设置中文环境变量，确保界面显示为中文
"""

import os
import sys
sys.path.insert(0, os.path.abspath(os.path.dirname(os.path.dirname(__file__))))

# 设置中文环境变量
os.environ['LANG'] = 'zh_CN.UTF-8'
os.environ['LANGUAGE'] = 'zh_CN:zh'
os.environ['LC_ALL'] = 'zh_CN.UTF-8'

# 导入事件引擎模块，用于处理系统内部的事件通信
from vnpy.event import EventEngine

# 导入主引擎模块，这是整个交易系统的核心引擎
from vnpy.trader.engine import MainEngine
# 导入主窗口和Qt应用创建函数，用于构建图形用户界面
from vnpy.trader.ui import MainWindow, create_qapp

# 导入CTP期货交易接口网关
from vnpy_ctp import CtpGateway
# 以下是其他各种交易接口网关的导入（当前被注释掉）
# from vnpy_ctptest import CtptestGateway  # CTP测试环境接口
# from vnpy_mini import MiniGateway  # 迷你期货接口
# from vnpy_femas import FemasGateway  # 飞马期货接口
# from vnpy_sopt import SoptGateway  # 期权接口
# from vnpy_sec import SecGateway  # 证券接口
# from vnpy_uft import UftGateway  # UFT接口
# from vnpy_esunny import EsunnyGateway  # 易盛接口
# from vnpy_xtp import XtpGateway  # XTP接口
# from vnpy_tora import ToraStockGateway  # 宽睿股票接口
# from vnpy_tora import ToraOptionGateway  # 宽睿期权接口
# from vnpy_comstar import ComstarGateway  # 银行间接口
# from vnpy_ib import IbGateway  # 盈透证券接口（暂时注释，避免ibapi安装问题）
# from vnpy_tap import TapGateway  # TAP接口
# from vnpy_da import DaGateway  # DA接口
# from vnpy_rohon import RohonGateway  # 融航接口
# from vnpy_tts import TtsGateway  # TTS接口
# from vnpy_ost import OstGateway  # OST接口
# from vnpy_hft import GtjaGateway  # 国泰君安高频接口
from vnpy_qmt.qmt_gateway import QmtGateway
from vnpy_xt import XtGateway


# 导入各种应用模块（当前被注释掉）
# from vnpy_paperaccount import PaperAccountApp  # 模拟账户应用
# from vnpy_ctastrategy import CtaStrategyApp  # CTA策略应用
from vnpy_ctabacktester import CtaBacktesterApp  # CTA回应用
# from vnpy_spreadtrading import SpreadTradingApp  # 价差交易应用
# from vnpy_algotrading import AlgoTradingApp  # 算法交易应用
# from vnpy_optionmaster import OptionMasterApp  # 期权大师应用
# from vnpy_portfoliostrategy import PortfolioStrategyApp  # 投资组合策略应用
# from vnpy_scripttrader import ScriptTraderApp  # 脚本交易应用
from vnpy_chartwizard import ChartWizardApp  # 图表精灵应用
# from vnpy_rpcservice import RpcServiceApp  # RPC服务应用
# from vnpy_excelrtd import ExcelRtdApp  # Excel实时数据应用（暂时注释，避免端口冲突）
from vnpy_datamanager import DataManagerApp  # 数据管理应用
# from vnpy_datarecorder import DataRecorderApp  # 数据记录应用
# from vnpy_riskmanager import RiskManagerApp  # 风险管理应用
from vnpy_webtrader import WebTraderApp  # 网页交易应用
# from vnpy_portfoliomanager import PortfolioManagerApp  # 投资组合管理应用


def main():
    """主函数：初始化并启动VeighNa交易系统（中文版）"""
    print("正在启动VeighNa交易系统（中文版）...")
    print(f"当前语言环境: {os.environ.get('LANG', '未设置')}")
    
    # 创建Qt应用程序实例
    qapp = create_qapp()

    # 创建事件引擎实例，用于处理系统内部事件
    event_engine = EventEngine()

    # 创建主引擎实例，传入事件引擎
    main_engine = MainEngine(event_engine)

    # 添加CTP期货交易网关到主引擎
    main_engine.add_gateway(CtpGateway)
    # 以下是其他交易网关的添加（当前被注释掉）
    # main_engine.add_gateway(CtptestGateway)  # 添加CTP测试环境网关
    # main_engine.add_gateway(MiniGateway)  # 添加迷你期货网关
    # main_engine.add_gateway(FemasGateway)  # 添加飞马期货网关
    # main_engine.add_gateway(SoptGateway)  # 添加期权网关
    # main_engine.add_gateway(SecGateway)  # 添加证券网关
    # main_engine.add_gateway(UftGateway)  # 添加UFT网关
    # main_engine.add_gateway(EsunnyGateway)  # 添加易盛网关
    # main_engine.add_gateway(XtpGateway)  # 添加XTP网关
    # main_engine.add_gateway(ToraStockGateway)  # 添加宽睿股票网关
    # main_engine.add_gateway(ToraOptionGateway)  # 添加宽睿期权网关
    # main_engine.add_gateway(OesGateway)  # 添加OES网关
    # main_engine.add_gateway(ComstarGateway)  # 添加银行间网关
    # main_engine.add_gateway(IbGateway)  # 添加盈透证券网关（暂时注释，避免ibapi安装问题）
    # main_engine.add_gateway(TapGateway)  # 添加TAP网关
    # main_engine.add_gateway(DaGateway)  # 添加DA网关
    # main_engine.add_gateway(RohonGateway)  # 添加融航网关
    # main_engine.add_gateway(TtsGateway)  # 添加TTS网关
    # main_engine.add_gateway(OstGateway)  # 添加OST网关
    # main_engine.add_gateway(NhFuturesGateway)  # 添加南华期货网关
    # main_engine.add_gateway(NhStockGateway)  # 添加南华股票网关
    main_engine.add_gateway(QmtGateway)  # 添加QMT网关
    main_engine.add_gateway(XtGateway)  # 添加XT网关

    # 添加各种应用模块到主引擎（当前被注释掉）
    # main_engine.add_app(PaperAccountApp)  # 添加模拟账户应用
    # main_engine.add_app(CtaStrategyApp)  # 添加CTA策略应用
    main_engine.add_app(CtaBacktesterApp)  # 添加CTA回测应用
    # main_engine.add_app(SpreadTradingApp)  # 添加价差交易应用
    # main_engine.add_app(AlgoTradingApp)  # 添加算法交易应用
    # main_engine.add_app(OptionMasterApp)  # 添加期权大师应用
    # main_engine.add_app(PortfolioStrategyApp)  # 添加投资组合策略应用
    # main_engine.add_app(ScriptTraderApp)  # 添加脚本交易应用
    main_engine.add_app(ChartWizardApp)  # 添加图表精灵应用
    # main_engine.add_app(RpcServiceApp)  # 添加RPC服务应用
    # main_engine.add_app(ExcelRtdApp)  # 添加Excel实时数据应用（暂时注释，避免端口冲突）
    # main_engine.add_app(DataManagerApp)  # 添加数据管理应用
    # main_engine.add_app(DataRecorderApp)  # 添加数据记录应用
    # main_engine.add_app(RiskManagerApp)  # 添加风险管理应用
    # main_engine.add_app(WebTraderApp)  # 添加网页交易应用
    # main_engine.add_app(PortfolioManagerApp)  # 添加投资组合管理应用

    # 创建主窗口实例，传入主引擎和事件引擎
    main_window = MainWindow(main_engine, event_engine)
    # 以最大化方式显示主窗口
    main_window.showMaximized()

    print("VeighNa交易系统启动成功！")
    # 启动Qt应用程序的事件循环
    qapp.exec()


# 程序入口点：当直接运行此脚本时执行main函数
if __name__ == "__main__":
    main()