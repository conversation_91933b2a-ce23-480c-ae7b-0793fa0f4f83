# VeighNa交易系统语言配置说明

## 问题描述

VeighNa交易系统支持国际化（i18n），界面语言会根据系统环境变量自动选择。如果您的界面显示为英文，这是因为系统环境变量被设置为英文。

## 解决方案

### 方案1：使用中文启动脚本（推荐）

我们为您创建了专门的中文启动脚本：

1. **Python脚本方式**：
   ```bash
   python run_chinese.py
   ```

2. **批处理文件方式**（Windows）：
   ```bash
   start_chinese.bat
   ```

### 方案2：手动设置环境变量

#### Windows系统

在PowerShell中运行：
```powershell
$env:LANG = "zh_CN.UTF-8"
$env:LANGUAGE = "zh_CN:zh"
$env:LC_ALL = "zh_CN.UTF-8"
python run.py
```

在CMD中运行：
```cmd
set LANG=zh_CN.UTF-8
set LANGUAGE=zh_CN:zh
set LC_ALL=zh_CN.UTF-8
python run.py
```

#### Linux/Mac系统

```bash
export LANG=zh_CN.UTF-8
export LANGUAGE=zh_CN:zh
export LC_ALL=zh_CN.UTF-8
python run.py
```

### 方案3：永久设置系统环境变量

#### Windows系统

1. 右键"此电脑" → "属性" → "高级系统设置" → "环境变量"
2. 在"用户变量"或"系统变量"中添加：
   - 变量名：`LANG`，变量值：`zh_CN.UTF-8`
   - 变量名：`LANGUAGE`，变量值：`zh_CN:zh`
   - 变量名：`LC_ALL`，变量值：`zh_CN.UTF-8`
3. 重启系统或重新登录

#### Linux系统

在 `~/.bashrc` 或 `~/.profile` 文件中添加：
```bash
export LANG=zh_CN.UTF-8
export LANGUAGE=zh_CN:zh
export LC_ALL=zh_CN.UTF-8
```

然后运行：
```bash
source ~/.bashrc
```

## 语言文件说明

VeighNa系统使用gettext进行国际化：

- **中文界面**：系统默认显示中文，无需额外翻译文件
- **英文界面**：需要英文翻译文件（`vnpy/trader/locale/en/LC_MESSAGES/vnpy.mo`）

## 当前配置

- ✅ 已创建中文启动脚本：`run_chinese.py`
- ✅ 已创建Windows批处理文件：`start_chinese.bat`
- ✅ 已注释掉有问题的模块（IB接口、ExcelRtdApp）
- ✅ 已启用基本功能模块（CTP、QMT、CTA策略、回测、数据管理）

## 推荐使用方式

1. **首次使用**：双击 `start_chinese.bat` 启动中文版
2. **日常使用**：运行 `python run_chinese.py`
3. **如果需要其他功能**：编辑 `run_chinese.py` 文件，取消相应模块的注释

## 注意事项

1. 如果启动时出现端口冲突错误，请检查是否有其他VeighNa实例在运行
2. 如果界面仍然显示英文，请检查环境变量是否正确设置
3. 某些模块可能需要额外的依赖包，请根据错误提示安装相应包

## 故障排除

### 问题1：界面仍然显示英文
**解决方案**：确保在启动Python脚本之前设置了正确的环境变量

### 问题2：启动时出现模块导入错误
**解决方案**：检查是否安装了相应的VeighNa模块包

### 问题3：端口被占用错误
**解决方案**：关闭其他VeighNa实例，或修改端口配置

### 问题4：中文显示乱码
**解决方案**：确保系统支持UTF-8编码，Windows系统建议使用PowerShell或设置代码页为65001 